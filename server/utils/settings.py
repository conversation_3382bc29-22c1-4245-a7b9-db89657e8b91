import json
import os
from datetime import timedelta
from enum import StrEnum
from functools import cached_property
from typing import Any

import airportsdata
import boto3
from dotenv import dotenv_values, load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()


class AgentTypes(StrEnum):
    FLIGHTS = "Flights"
    EXCHANGE_FLIGHTS = "ExchangeFlights"
    CANCEL_FLIGHTS = "CancelFlights"
    HOTELS = "Hotels"
    CANCEL_HOTEL = "CancelHotel"
    OTHER = "Other"
    POST_BOOKING = "PostBooking"
    ERROR = "Error"
    FOH = "FOH"
    CHANGE_SEAT = "ChangeSeat"
    PREFERENCES = "Preferences"
    CANCELLATION = "Cancellation"


class Settings(BaseSettings):
    OTTO_ENV: str = os.getenv("OTTO_ENV") or "DEV"
    ONBOARDING_THREAD_TITLE: str = "Onboarding Preferences"
    PREFERENCES_THREAD_TITLE: str = "Travel Preferences"
    TRAVEL_POLICY_THREAD_TITLE: str = "Travel Policy"
    FUTURE_TRIPS_THREAD_TITLE: str = "Future Trips"
    ONBOARDING_PAGE_TITLE: str = "Onboarding"
    GETTING_STARTED_TITLE: str = "Getting Started"
    NEW_TRIP_TITLE: str = "New Trip"
    ACCESS_TOKEN_LIFESPAN: timedelta = timedelta(days=400)
    REFRESH_TOKEN_LIFESPAN: timedelta = timedelta(days=400)
    PARTNER_SPOTNANA_TOKEN_LIFESPAN: timedelta = timedelta(days=1)
    SPOTNANA_UNUSED_CREDIT_CACHE_LIFESPAN: timedelta = timedelta(hours=1)
    ONE_TIME_PASSWORD_LIFESPAN: timedelta = timedelta(minutes=10)
    ONE_TIME_CODE_LENGTH: int = 12

    client_domain: str = os.getenv("CLIENT_DOMAIN") or ""
    redis_url: str = os.getenv("REDIS_URL") or ""

    enable_aws_spotnana_proxy: bool = bool(os.getenv("ENABLE_AWS_SPOTNANA_PROXY") or "false")

    CARD_EXPIRE_DELTA: timedelta = timedelta(hours=1)
    FLIGHT_CARD_EXPIRE_DELTA: timedelta = timedelta(hours=4)
    MIN_MESSAGE_TIMESTAMP_DELTA: timedelta = timedelta(hours=1)

    AGENT_MESSAGE_COLOR_MAP: dict[AgentTypes, str] = {
        AgentTypes.FLIGHTS: "green-500",
        AgentTypes.EXCHANGE_FLIGHTS: "green-300",
        AgentTypes.CANCEL_FLIGHTS: "green-600",
        AgentTypes.HOTELS: "blue-500",
        AgentTypes.CANCEL_HOTEL: "blue-600",
        AgentTypes.OTHER: "orange-500",
        AgentTypes.POST_BOOKING: "purple-300",
        AgentTypes.ERROR: "red-500",
        AgentTypes.FOH: "purple-500",
        AgentTypes.CHANGE_SEAT: "purple-700",
        AgentTypes.PREFERENCES: "primary-400",
        AgentTypes.CANCELLATION: "primary-600",
    }

    EMAIL_ADDRESS_AFTER_PLUS_SIGN_PREFIX: str = "threadid"

    # IMPORTANT: keep this in sync with the metro area codes "MAC_CODE" in flight_planner.baml
    METRO_AREA_CODES: dict[str, list[str]] = {
        # Asia
        "BJS": ["PEK", "PKX"],
        "JKT": ["CGK", "HLP"],
        "OSA": ["KIX", "ITM"],
        "SPK": ["CTS", "OKD"],
        "SEL": ["ICN", "GMP"],
        "TYO": ["NRT", "HND"],
        # Europe
        "BER": ["BER"],
        "BUH": ["OTP", "BBU"],
        "EAP": ["BSL", "MLH"],
        "LON": ["LHR", "LGW", "STN", "LTN", "LCY", "SEN", "BQH", "QQS"],  # Ordered by passenger traffic
        "MIL": ["MXP", "LIN"],
        "MOW": ["SVO", "DME", "VKO"],
        "PAR": ["CDG", "ORY", "LBG"],
        "ROM": ["FCO", "CIA"],
        "STO": ["ARN", "NYO", "BMA"],
        # North America
        "CHI": ["ORD", "MDW"],
        "DTT": ["DTW", "YIP"],
        "QHO": ["IAH", "HOU"],
        "QLA": ["LAX", "ONT", "SNA", "BUR"],
        "QMI": ["MIA", "FLL", "PBI"],
        "YMQ": ["YUL", "YMY"],
        "NYC": ["JFK", "EWR", "LGA"],
        "QSF": ["SFO", "OAK", "SJC"],
        "YTO": ["YYZ", "YTZ"],
        "WAS": ["IAD", "DCA", "BWI"],
        # below are hardedcoded
        "DFW": ["DFW", "DAL"],
        "MIA": ["MIA", "FLL"],
        "YVR": ["YVR", "YXX"],
        "MEX": ["MEX", "TLC", "NLU"],
        # South America
        "BUE": ["EZE", "AEP"],
        "RIO": ["GIG", "SDU"],
        "SAO": ["GRU", "CGH", "VCP"],
    }

    US_METRO_AREA_CODES: list[str] = ["CHI", "DTT", "QHO", "QLA", "QMI", "NYC", "QSF", "WAS"]

    # airport codes to metro area codes
    AIRPORT_TO_METRO_MAP: dict[str, str] = {}

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._build_airport_to_metro_map()

    def _build_airport_to_metro_map(self) -> None:
        """Populates the AIRPORT_TO_METRO_MAP dictionary with mappings from airport codes to metro area codes."""
        for metro_code, airport_codes in self.METRO_AREA_CODES.items():
            for airport_code in airport_codes:
                self.AIRPORT_TO_METRO_MAP[airport_code] = metro_code

    GOOGLE_CALENDAR_EVENTS_KEYWORDS: str = "flight, hotel, stay, trip"

    # The group id already generated in DB for automation tests
    AUTOMATION_TESTS_OTC_GROUP_ID: int = 1
    AUTOMATION_TESTS_OTC_EMAILS: list[str] = ["<EMAIL>", "<EMAIL>"]
    AUTOMATION_TESTS_OTP: str = "111777"

    class Config:
        env_file = ".env"
        extra = "allow"

    @property
    def OTTO_SELF_INTRO(self) -> str:
        return self.all_secrets_dict.get(
            "OTTO_SELF_INTRO",
            "You are Otto, a professional corporate travel consultant assisting frequent business travelers. You support busy executives with travel planning across flights, hotels, and visas.",
        )

    @property
    def CAPABILITY_SUGGESTION_PERIOD(self) -> timedelta:
        seconds = (
            self.all_secrets_dict.get("CAPABILITY_SUGGESTION_PERIOD_SECONDS") or 12 * 60 * 60
        )  # default to 12 hours in seconds
        return timedelta(seconds=int(seconds))

    @property
    def CAPABILITY_SUGGESTION_EXPIRATION_PERIOD(self) -> timedelta:
        seconds = (
            self.all_secrets_dict.get("CAPABILITY_SUGGESTION_EXPIRATION_PERIOD_SECONDS") or 7 * 24 * 60 * 60
        )  # default to 7 days (1 weeks) in seconds
        return timedelta(seconds=int(seconds))

    @property
    def OTTO_CONVO_STYLE(self) -> str:
        return self.all_secrets_dict.get(
            "OTTO_CONVO_STYLE",
            "You speak with efficiency, confidence, and experience—like someone who has booked thousands of trips and knows what matters. "
            "You recommend options clearly and proactively, and you avoid generic language like “good option” or “smooth itinerary.” "
            "Instead, explain why something works well. Your tone is polished but natural, like a trusted human assistant on the phone. "
            "Never repeat yourself or over-explain. Anticipate what the traveler needs next and move the process forward without prompting. "
            "Prioritize clarity, timing, and business convenience in every recommendation. You respond with the language the user used in the last message. "
            "If no further instruction is given, keep responses under 25 words.",
        )

    @property
    def EMAIL_REPHRASE_ENABLED(self) -> bool:
        v: str = self.all_secrets_dict.get("EMAIL_REPHRASE_ENABLED", "True")
        return v.lower() in ("yes", "true", "t", "1")

    @property
    def MAIL_GUN_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("MAIL_GUN_API_KEY")

    @property
    def is_dev_env(self) -> bool:
        """Check if current environment is DEV"""
        return self.OTTO_ENV.upper() == "DEV"

    @property
    def secrets_manager(self) -> Any:
        return boto3.client(service_name="secretsmanager")

    @property
    def aws_secrets_dict(self) -> dict[str, str]:
        secret_name: str = f"otto-server-{self.OTTO_ENV.lower()}"
        aws_secret: dict[str, str] = self.secrets_manager.get_secret_value(SecretId=secret_name)

        if "SecretString" in aws_secret:
            secret_data_str: str = aws_secret["SecretString"]
            secret_data = json.loads(secret_data_str)
            return secret_data

        return {}

    @cached_property
    def all_secrets_dict(self) -> dict[str, Any]:
        env_secrets = dotenv_values()
        return {**self.aws_secrets_dict, **env_secrets}

    @property
    def RUN_CONTEXT(self) -> str | None:
        return self.all_secrets_dict.get("RUN_CONTEXT")

    # Percentage of price difference between the flight and the candidate flight. Ex: 20 means 20%
    @property
    def SERP_SPOTNANA_FLIGHT_PRICE_DIFF_THRESHOLD(self) -> float:
        return float(self.all_secrets_dict.get("SERP_SPOTNANA_FLIGHT_PRICE_DIFF_THRESHOLD", 20.0))

    @property
    def SERP_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("SERP_API_KEY")

    @property
    def SERP_FLIGHT_TOKEN_CACHE_TTL(self) -> int:
        return int(self.all_secrets_dict.get("SERP_FLIGHT_TOKEN_CACHE_TTL", 5))

    @property
    def SERP_FALLBACK_THRESHOLD(self) -> int:
        return int(self.all_secrets_dict.get("SERP_FALLBACK_THRESHOLD", 2))

    @property
    def SPOTNANA_FLIGHT_BY_DEFAULT(self) -> bool:
        v: str = self.all_secrets_dict.get("SPOTNANA_FLIGHT_BY_DEFAULT", "False")
        return v.lower() in ("yes", "true", "t", "1")

    @property
    def OTTO_APP_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("OTTO_APP_SECRET")

    @property
    def PG_USER(self) -> str | None:
        return self.all_secrets_dict.get("PG_USER")

    @property
    def SENTRY_DSN(self) -> str | None:
        return self.all_secrets_dict.get("SENTRY_DSN")

    @property
    def PG_PASSWORD(self) -> str | None:
        return self.all_secrets_dict.get("PG_PASSWORD")

    @property
    def PG_HOST(self) -> str | None:
        return self.all_secrets_dict.get("PG_HOST")

    @property
    def PG_PORT(self) -> str | None:
        return self.all_secrets_dict.get("PG_PORT")

    @property
    def PG_DATABASE(self) -> str | None:
        return self.all_secrets_dict.get("PG_DATABASE")

    @property
    def GOOGLE_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_CLIENT_ID")

    @property
    def GOOGLE_MOBILE_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_MOBILE_CLIENT_ID")

    @property
    def GOOGLE_CLIENT_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_CLIENT_SECRET")

    @property
    def OUTLOOK_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("OUTLOOK_CLIENT_ID")

    @property
    def OUTLOOK_CLIENT_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("OUTLOOK_CLIENT_SECRET")

    @property
    def SERVER_DNS(self) -> str | None:
        return self.all_secrets_dict.get("SERVER_DNS")

    @property
    def OPENAI_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("OPENAI_API_KEY")

    @property
    def CLIENT_DOMAIN(self) -> str:
        if self.client_domain:
            return self.client_domain

        return self.all_secrets_dict.get("CLIENT_DOMAIN") or "https://app.ottotheagent.com/"

    @property
    def GOOGLE_GEMINI_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_GEMINI_API_KEY")

    @property
    def AZURE_OPENAI_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_OPENAI_API_KEY")

    @property
    def AZURE_EASTUS_OPENAI_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_EASTUS_OPENAI_API_KEY")

    @property
    def HUGGINGFACE_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("HUGGINGFACE_API_KEY")

    @property
    def MONGO_USER(self) -> str | None:
        return self.all_secrets_dict.get("MONGO_USER")

    @property
    def MONGO_PASSWORD(self) -> str | None:
        return self.all_secrets_dict.get("MONGO_PASSWORD")

    @property
    def MAILGUN_REPLYTO_EMAIL_PREFIX(self) -> str:
        return self.all_secrets_dict.get("MAILGUN_REPLYTO_EMAIL_PREFIX", "travel-" + self.OTTO_ENV.lower())

    @property
    def MAILGUN_REPLYTO_EMAIL_DOMAIN(self) -> str:
        return self.all_secrets_dict.get("MAILGUN_REPLYTO_EMAIL_DOMAIN", "notify.ottotheagent.com")

    @property
    def MAILGUN_WEBHOOK_SIGNING_KEY(self) -> str | None:
        return self.all_secrets_dict.get("MAILGUN_WEBHOOK_SIGNING_KEY")

    @property
    def MAILGUN_WEBHOOK_SIGNING_EXPIRED_AFTER(self) -> int:
        return int(self.all_secrets_dict.get("MAILGUN_WEBHOOK_SIGNING_EXPIRED_AFTER", 15))  # default to 15 minutes

    @property
    def MONGO_HOST(self) -> str | None:
        return self.all_secrets_dict.get("MONGO_HOST")

    @property
    def MONGO_PORT(self) -> str | None:
        return self.all_secrets_dict.get("MONGO_PORT")

    @property
    def MONGO_DATABASE(self) -> str | None:
        return self.all_secrets_dict.get("MONGO_DATABASE")

    @property
    def NEO4J_USER(self) -> str | None:
        return self.all_secrets_dict.get("NEO4J_USER")

    @property
    def NEO4J_PASSWORD(self) -> str | None:
        return self.all_secrets_dict.get("NEO4J_PASSWORD")

    @property
    def NEO4J_URL(self) -> str | None:
        return self.all_secrets_dict.get("NEO4J_URL")

    @property
    def ALLOWED_DOMAINS(self) -> str | None:
        return self.all_secrets_dict.get("ALLOWED_DOMAINS")

    @property
    def COOKIES_DOMAIN(self) -> str | None:
        return self.all_secrets_dict.get("COOKIES_DOMAIN")

    @property
    def LOG_GROUP_NAME(self) -> str | None:
        return self.all_secrets_dict.get("LOG_GROUP_NAME")

    @property
    def CLOUDWATCH_METRICS_NAMESPACE(self) -> str | None:
        return self.all_secrets_dict.get("CLOUDWATCH_METRICS_NAMESPACE")

    @property
    def GOOGLE_MAPS_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_MAPS_API_KEY")

    @property
    def GOOGLE_PLACE_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("GOOGLE_PLACE_API_KEY")

    @property
    def SPOTNANA_HOST(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_HOST")

    @property
    def SPOTNANA_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_CLIENT_ID")

    @property
    def ANDROID_AUTH_KEY(self) -> str | None:
        return self.all_secrets_dict.get("ANDROID_AUTH_KEY")

    @property
    def IOS_AUTH_KEY(self) -> str | None:
        return self.all_secrets_dict.get("IOS_AUTH_KEY")

    @property
    def APP_ID_IOS(self) -> str | None:
        return self.all_secrets_dict.get("APP_ID_IOS")

    @property
    def APP_ID_ANDROID(self) -> str | None:
        return self.all_secrets_dict.get("APP_ID_ANDROID")

    @property
    def SPOTNANA_CLIENT_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_CLIENT_SECRET")

    @property
    def SPOTNANA_COMPANY_GUID(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_COMPANY_GUID")

    @property
    def SPOTNANA_COMPANY_LEGAL_ID(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_COMPANY_LEGAL_ID")

    @property
    def SPOTNANA_USER_GUID(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_USER_GUID")

    @property
    def SPOTNANA_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_API_KEY")

    @property
    def AWS_SPOTNANA_PROXY(self) -> str | None:
        if not self.enable_aws_spotnana_proxy:
            return None
        return self.all_secrets_dict.get("AWS_SPOTNANA_PROXY")

    @property
    def SPOTNANA_VGS_HOST(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_VGS_HOST")

    @property
    def OTTO_VGS_INBOUND_HOST(self) -> str | None:
        return self.all_secrets_dict.get("OTTO_VGS_INBOUND_HOST")

    @property
    def OTTO_VGS_OUTBOUND_HOST(self) -> str | None:
        return self.all_secrets_dict.get("OTTO_VGS_OUTBOUND_HOST")

    @property
    def BOOKING_DOT_COM_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("BOOKING_DOT_COM_API_KEY")

    @property
    def BOOKING_DOT_COM_AFFILIATE_ID(self) -> str | None:
        return self.all_secrets_dict.get("BOOKING_DOT_COM_AFFILIATE_ID")

    @property
    def SMTP_USERNAME(self) -> str | None:
        return self.all_secrets_dict.get("SMTP_USERNAME")

    @property
    def SMTP_PASSWORD(self) -> str | None:
        return self.all_secrets_dict.get("SMTP_PASSWORD")

    @property
    def RECEIVER_EMAIL(self) -> str | None:
        return self.all_secrets_dict.get("RECEIVER_EMAIL")

    @property
    def SENTRY_PROJECT_ID(self) -> str | None:
        return self.all_secrets_dict.get("SENTRY_PROJECT_ID")

    @property
    def SENTRY_KEY(self) -> str | None:
        return self.all_secrets_dict.get("SENTRY_KEY")

    @property
    def HOTEL_FAKE_BOOKING(self) -> bool:
        # default to real booking to avoid production outage
        hotel_fake_booking: str = self.all_secrets_dict.get("HOTEL_FAKE_BOOKING", "False")
        return hotel_fake_booking.lower() in ("yes", "true", "t", "1")

    @property
    def PARTNER_SPOTNANA_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("PARTNER_SPOTNANA_CLIENT_ID")

    @property
    def PARTNER_SPOTNANA_CLIENT_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("PARTNER_SPOTNANA_CLIENT_SECRET")

    @property
    def PARTNER_SPOTNANA_TOKEN_SIGN_SECRET(self) -> str | None:
        return self.all_secrets_dict.get("PARTNER_SPOTNANA_TOKEN_SIGN_SECRET")

    @property
    def AZURE_GPT4OMINI_DEPLOYMENT(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_GPT4OMINI_DEPLOYMENT")

    @property
    def AZURE_ENDPOINT(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_ENDPOINT")

    @property
    def AZURE_API_VERSION(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_API_VERSION")

    @property
    def AZURE_EMBEDDING_DEPLOYMENT(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_EMBEDDING_DEPLOYMENT")

    @property
    def AZURE_EMBEDDING_ENDPOINT(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_EMBEDDING_ENDPOINT")

    @property
    def AZURE_EMBEDDING_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("AZURE_EMBEDDING_API_KEY")

    @property
    def ZEP_API_KEY(self) -> str | None:
        return "z_1dWlkIjoiNGZkOGI5MjMtYmZkYS00MDBlLTgwNWUtNGU5ZmUzYWRiZmFkIn0.OAnv2-vI8ONwuPyqi40_2LA95cOSGMlyryqCtmENccZvPjTnlYakFQkH6do_I3ffQga5Gif65XoLJf1jchW9yQ"

    @property
    def SITATA_API_HOST(self) -> str | None:
        # don't put / at the end of the url
        return self.all_secrets_dict.get("SITATA_API_HOST")

    @property
    def SITATA_ORG_ID(self) -> str | None:
        return self.all_secrets_dict.get("SITATA_ORG_ID")

    @property
    def SITATA_AUTH_TOKEN(self) -> str | None:
        return self.all_secrets_dict.get("SITATA_AUTH_TOKEN")

    @property
    def SILVERRAILE_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("SILVERRAILE_API_KEY")

    @property
    def STAGING_SPOTNANA_WEBHOOK_URL(self) -> str | None:
        # only exists in prod do webhook double dispatch
        return "https://api.stg.otto-demo.com/api/partners/spotnana"

    @property
    def STAGING_SPOTNANA_AUTH_CLIENT_ID(self) -> str | None:
        # only exists in prod do webhook double dispatch
        return self.all_secrets_dict.get("STAGING_SPOTNANA_AUTH_CLIENT_ID")

    @property
    def STAGING_SPOTNANA_AUTH_CLIENT_SECRET(self) -> str | None:
        # only exists in prod do webhook double dispatch
        return self.all_secrets_dict.get("STAGING_SPOTNANA_AUTH_CLIENT_SECRET")

    @property
    def CRON_EXPR_CHECK_HOTEL_STATUS(self) -> str | None:
        return self.aws_secrets_dict.get("CRON_EXPR_CHECK_HOTEL_STATUS")

    @property
    def SPOTNANA_WEB_PORTAL_URL(self) -> str | None:
        return self.all_secrets_dict.get("SPOTNANA_WEB_PORTAL_URL")

    @property
    def ZAPIER_WEBHOOK(self) -> str | None:
        return self.all_secrets_dict.get("ZAPIER_WEBHOOK")

    @property
    def APPLE_CLIENT_ID(self) -> str | None:
        return self.all_secrets_dict.get("APPLE_CLIENT_ID")

    @property
    def APPLE_TEAM_ID(self) -> str | None:
        return self.all_secrets_dict.get("APPLE_TEAM_ID")

    @property
    def APPLE_KEY_ID(self) -> str | None:
        return self.all_secrets_dict.get("APPLE_KEY_ID")

    @property
    def APPLE_PRIVATE_KEY(self) -> str | None:
        return self.all_secrets_dict.get("APPLE_PRIVATE_KEY")

    @property
    def TEMPORAL_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("TEMPORAL_API_KEY")

    @property
    def TEMPORAL_NS(self) -> str | None:
        return self.all_secrets_dict.get("TEMPORAL_NS")

    @property
    def TEMPORAL_HOST(self) -> str | None:
        return self.all_secrets_dict.get("TEMPORAL_HOST")

    @property
    def ANTHROPIC_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("ANTHROPIC_API_KEY")

    @property
    def GMAIL_SENDER_SERVICE_ACCOUNT_JSON(self) -> str | None:
        return self.all_secrets_dict.get("GMAIL_SENDER_SERVICE_ACCOUNT_JSON")

    @property
    def TRIP_ARTIFACT_BUCKET_NAME(self) -> str:
        return self.all_secrets_dict.get("TRIP_ARTIFACT_BUCKET_NAME", "otto-debug-artifacts")

    @property
    def REDIS_URL(self) -> str | None:
        if self.redis_url:
            return self.redis_url

        return self.all_secrets_dict.get("REDIS_URL")

    @property
    def PARTNER_PROMETHEUS_TOKEN(self) -> str | None:
        return self.all_secrets_dict.get("PARTNER_PROMETHEUS_TOKEN")

    @property
    def IPSTACK_ACCESS_KEY(self) -> str | None:
        return self.all_secrets_dict.get("IPSTACK_ACCESS_KEY")

    @property
    def MAILERCHECK_API_KEY(self) -> str | None:
        return self.all_secrets_dict.get("MAILERCHECK_API_KEY")

    @property
    def INVOICE_S3_BUCKET(self) -> str:
        return self.all_secrets_dict.get("INVOICE_S3_BUCKET", "otto-trip-invoices")

    @property
    def HIDE_SAMPLES_AFTER_N_SEARCHES(self) -> int:
        return self.all_secrets_dict.get("HIDE_SAMPLES_AFTER_N_SEARCHES", 2)

    @property
    def MIXPANEL_TOKEN(self):
        return self.all_secrets_dict.get("MIXPANEL_TOKEN")

    @property
    def BOUNDARY_PROJECT_ID(self):
        return self.all_secrets_dict.get("BOUNDARY_ML_PROJECT")

    @property
    def BOUNDARY_SECRET(self):
        return self.all_secrets_dict.get("BOUNDARY_ML_SECRET")

    @property
    def FLIGHTAWARE_API_KEY(self):
        return self.all_secrets_dict.get("FLIGHTAWARE_API_KEY")


settings = Settings()
os.environ["OPENAI_API_KEY"] = settings.OPENAI_API_KEY or ""
os.environ["GOOGLE_GEMINI_API_KEY"] = settings.GOOGLE_GEMINI_API_KEY or ""
os.environ["AZURE_OPENAI_API_KEY"] = settings.AZURE_OPENAI_API_KEY or ""
os.environ["AZURE_EASTUS_OPENAI_API_KEY"] = settings.AZURE_EASTUS_OPENAI_API_KEY or ""
os.environ["HUGGINGFACE_API_KEY"] = settings.HUGGINGFACE_API_KEY or ""
os.environ["ANTHROPIC_API_KEY"] = settings.ANTHROPIC_API_KEY or ""

os.environ["LLM_AZURE_OPENAI_API_KEY"] = settings.AZURE_OPENAI_API_KEY or ""
os.environ["LLM_AZURE_DEPLOYMENT"] = settings.AZURE_GPT4OMINI_DEPLOYMENT or ""
os.environ["LLM_AZURE_ENDPOINT"] = settings.AZURE_ENDPOINT or ""
os.environ["LLM_AZURE_API_VERSION"] = settings.AZURE_API_VERSION or ""

os.environ["EMBEDDING_AZURE_OPENAI_API_KEY"] = settings.AZURE_EMBEDDING_API_KEY or ""
os.environ["EMBEDDING_AZURE_DEPLOYMENT"] = settings.AZURE_EMBEDDING_DEPLOYMENT or ""
os.environ["EMBEDDING_AZURE_ENDPOINT"] = settings.AZURE_EMBEDDING_ENDPOINT or ""
os.environ["EMBEDDING_AZURE_API_VERSION"] = settings.AZURE_API_VERSION or ""

os.environ["BOUNDARY_PROJECT_ID"] = settings.BOUNDARY_PROJECT_ID or ""
os.environ["BOUNDARY_SECRET"] = settings.BOUNDARY_SECRET or ""

os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = settings.OTTO_ENV.lower()
os.environ["LANGFUSE_SECRET_KEY"] = settings.all_secrets_dict.get("LANGFUSE_SECRET_KEY", "")
os.environ["LANGFUSE_PUBLIC_KEY"] = settings.all_secrets_dict.get("LANGFUSE_PUBLIC_KEY", "")
os.environ["LANGFUSE_HOST"] = settings.all_secrets_dict.get("LANGFUSE_HOST", "https://us.cloud.langfuse.com")

if settings.RUN_CONTEXT == "server":
    os.environ["BAML_LOG_JSON"] = "1"

# Set this to relax the token scope for google calendar, or the get_calendar_token will fail if user doesn't grant all scopes during oauth
os.environ["OAUTHLIB_RELAX_TOKEN_SCOPE"] = "1"


airports_info = airportsdata.load("IATA")

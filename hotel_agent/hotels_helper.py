import asyncio
import json
from copy import deepcopy
from datetime import datetime, timezone
from enum import Enum
from io import String<PERSON>
from typing import Any, Callable, List, Optional, TypedDict

from langchain_core.messages import AIMessage, FunctionMessage
from pydantic import BaseModel, Field

from baml_client import b
from baml_client.types import (
    CancelHotelResponseWithStep,
    HotelBookingResponse,
    HotelOrderPreview,
    HotelPolicy,
    HotelSearchOptionsResponse,
    HotelSelectResult,
)
from hotel_agent.booking_dot_com_models import ApiResponseStatus, BookingStatus
from hotel_agent.booking_dot_com_tools import (
    BookingTools,
    HotelBookingPaymentTiming,
    booking_failed_status,
    distance_from_target_location_threshold,
    one_mile_in_meters,
    radius_expanding_step,
)
from server.database.models.bookings import Booking
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.schemas.authenticate.user import User
from server.services.memory.trips.memory_modules.bookings_memory import BookingsMemory
from server.services.trips.bookings import (
    get_accommodation_booking,
)
from server.services.user.user_preferences import get_user_preferences
from server.utils.logger import logger
from server.utils.message_constants import HOTEL_SKELETON_MESSAGES
from server.utils.mongo_connector import converse_hotel_state_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import (
    console_masks,
    get_current_date_string,
    get_current_datetime_string,
)
from virtual_travel_agent.timings import Timings

hotel_log_mask = console_masks["hotel"]
number_of_hotels_considered_by_the_model = 25
max_llm_selected_hotels = 4
max_llm_selected_rooms = 3
VGS_URL = settings.OTTO_VGS_OUTBOUND_HOST


class HotelBookingParams(TypedDict):
    order_token: str
    room_product_id: str
    payment_timing: HotelBookingPaymentTiming | None


class HotelState(BaseModel):
    check_in_date: str | None = None
    check_out_date: str | None = None
    property_id: int | None = None
    room_product_id: str | None = None
    hotel_order_id: str | None = None
    room_title: str = ""
    hotel_name: str = ""
    did_hotel_search: bool = False
    order_token: str | None = None
    gathered_required_information_for_hotel_search: bool = False
    hotel_selected: bool = False
    room_product_selected: bool = False
    traveler_updated_search_criteria: bool = False
    did_order_preview: bool = False
    validated_price: Any | None = None
    did_order_create: bool = False
    cancel_hotel_state: CancelHotelResponseWithStep = Field(default_factory=lambda: CancelHotelResponseWithStep())
    _is_dirty: bool = False


class HotelSearchType(Enum):
    REGULAR = "regular"
    EQUIVALENT = "equivalent"


class HotelsHelper:
    def __init__(
        self,
        user: User,
        thread_id: int,
        timezone: str | None = None,
    ):
        self.input_tokens_counter = 0
        self.output_tokens_counter = 0
        self.user = user
        self.timezone = timezone
        self.booking_dot_com_tools = BookingTools(user=user)
        # The caches are static so they should only be loaded once per runtime
        self.booking_dot_com_tools.load_caches()
        self.topic: str = "Hotels"
        self.hotel_vgs_url = VGS_URL
        self.thread_id = thread_id
        self.bookings_memory = BookingsMemory(user_id=str(user.id), thread_id=str(thread_id))

        self._state = HotelState()

        # Define properties for transparent access to state fields
        for field_name in self._state.model_fields:
            # This creates a property for each field dynamically
            setattr(HotelsHelper, field_name, self._create_state_property(field_name))

    @classmethod
    def _create_state_property(cls, field_name):
        """Create a property that redirects to the state model."""

        def getter(self):
            return getattr(self._state, field_name)

        def setter(self, value):
            setattr(self._state, field_name, value)
            setattr(self._state, "_is_dirty", True)

        return property(getter, setter)

    async def persist_converse_hotel_state(self):
        if not self._state._is_dirty:
            logger.info("No hotel state changes to persist", mask=hotel_log_mask)
            return

        try:
            # Special handling for the cancel_hotel_state field
            state_dict = self._state.model_dump(exclude={"_is_dirty"})
            if isinstance(state_dict["cancel_hotel_state"], CancelHotelResponseWithStep):
                state_dict["cancel_hotel_state"] = state_dict["cancel_hotel_state"].model_dump_json()

            await converse_hotel_state_collection.update_one(
                {"thread_id": self.thread_id, "user_id": self.user.id},
                {"$set": state_dict},
                upsert=True,
            )
            logger.info("Successfully persisted converse hotel state", mask=hotel_log_mask)
        except Exception as e:
            logger.error(f"Error persisting converse hotel state: {e}", mask=hotel_log_mask)

    async def load_converse_hotel_state(self):
        try:
            converse_hotel_state = await converse_hotel_state_collection.find_one({"thread_id": self.thread_id})
            if converse_hotel_state:
                # Handle cancel_hotel_state special case
                if "cancel_hotel_state" in converse_hotel_state and isinstance(
                    converse_hotel_state["cancel_hotel_state"], str
                ):
                    converse_hotel_state["cancel_hotel_state"] = CancelHotelResponseWithStep(
                        **json.loads(converse_hotel_state["cancel_hotel_state"])
                    )

                # Update the state model with loaded values
                self._state = HotelState.model_validate(converse_hotel_state)
            else:
                self._state = HotelState()

        except Exception as e:
            logger.error(f"Error loading converse hotel state: {e}", mask=hotel_log_mask)

    async def build_hotel_cancel_context(self, order_id: str | None) -> str:
        hotel_cancel_context = ""
        if order_id is None:
            hotel_cancel_context = "I'm sorry, Can you specify which hotel or room you'd like to cancel?"
        else:
            order_details = await self.booking_dot_com_tools.get_order_details(order_id)

            match order_details.status:
                case BookingStatus.CANCELLED:
                    hotel_cancel_context = "This booking can't be cancelled because it is already cancelled."
                case BookingStatus.CANCELLED_BY_ACCOMMODATION:
                    hotel_cancel_context = "This booking can't be cancelled because it is cancelled by the hotel."
                case BookingStatus.CANCELLED_BY_GUEST:
                    hotel_cancel_context = "This booking can't be cancelled because it is cancelled by user."
                case BookingStatus.STAYED:
                    hotel_cancel_context = "This booking can't be cancelled because the guest has already stayed."
                case BookingStatus.BOOKED:
                    formatted_response = await b.CombineText(
                        firstText=f"This booking can be cancelled. Here is the cancellation policy: {[policy.model_dump_json() for policy in order_details.cancellation_policies]}",
                        secondText="Would you like to proceed and cancel this hotel reservation?",
                        other_instruction=None,
                    )
                    hotel_cancel_context = formatted_response.combinedText
                case _:
                    hotel_cancel_context = (
                        order_details.error_message
                        or "Sorry, I couldn't retrieve the booking status. Please try again later."
                    )

        return hotel_cancel_context

    async def cancel_hotel(self, order_id: str, hotel_cancel_reason: str):
        logger.info("do_cancel_hotel", mask=hotel_log_mask)

        if hotel_cancel_reason:
            hotel_cancel_reason = "Cancelled by user"

        response = await self.booking_dot_com_tools.cancel_order(order_id, hotel_cancel_reason)

        if response.status == ApiResponseStatus.SUCCESS:
            logger.info(f"Successfully cancelled order {order_id}", mask=hotel_log_mask)

            booking = await get_accommodation_booking(order_id)
            if booking:
                await self.update_booking_status(order_id, BookingStatus.CANCELLED)
        else:
            logger.error(f"Failed to cancel order {order_id}: {response.model_dump_json()}", mask=hotel_log_mask)
        return response

    async def update_booking_status(self, order_id: str, status: BookingStatus):
        update_query = {
            "thread_id": self.thread_id,
            "type": "accommodations",
            "content.order_number": order_id,
        }
        update_data = {
            "content.status": status.value.lower(),
            "status": status.name,
        }
        await Booking.update_fields(update_query, update_data)

    async def get_available_hotels_and_price(
        self,
        tool_input: dict,
        lat_long_dict: dict,
        start_radius: float | None = radius_expanding_step,
        max_radius: float = distance_from_target_location_threshold,
        hotel_policy: HotelPolicy | None = None,
        preferred_payment_timings: List[HotelBookingPaymentTiming] | None = None,
        is_mobile: bool = False,
        schedule_send_message_fn: Callable[[dict[str, Any]], None] | None = None,
    ) -> tuple[list, float, list]:
        """
        Search for hotels and check their availability, automatically expanding search radius if needed.
        Returns tuple of (available_hotels, final_search_radius)
        """

        current_radius = start_radius or radius_expanding_step
        city_name: str | None = None
        company_rate = None

        if not self.check_in_date or not self.check_out_date:
            raise ValueError("Missing check-in or check-out date")

        start_date = datetime.strptime(self.check_in_date, "%Y-%m-%d") if self.check_in_date else None
        end_date = datetime.strptime(self.check_out_date, "%Y-%m-%d") if self.check_out_date else None

        nights = 1
        if start_date and end_date:
            nights = (end_date - start_date).days

        all_hotels = []
        already_checked_not_available_hotels_ids: set[Any] = set()
        while current_radius <= max_radius:
            # Search for hotels within current radius
            all_hotels, _ = await self.booking_dot_com_tools.do_base_search_v2(
                tool_input,
                lat_long_dict,
                distance_threshold=int(current_radius),
                preferred_payment_timings=preferred_payment_timings,
            )

            # Filter out hotels that are already checked and not available
            hotels_to_check = [
                h for h in all_hotels if h["property_id"] not in already_checked_not_available_hotels_ids
            ]

            # If no hotels found, return an empty list
            if not hotels_to_check or len(hotels_to_check) == 0:
                logger.warn(
                    f"No {'new ' if already_checked_not_available_hotels_ids else ' '}hotels found within {current_radius} meters from {lat_long_dict} in mongo",
                    mask=hotel_log_mask,
                )
                current_radius += radius_expanding_step
                continue
            is_mobile = tool_input.get("is_mobile", False)
            # Check availability and pricing
            available_hotels = await self.booking_dot_com_tools.get_availability_and_price(
                hotel_options=hotels_to_check,
                check_in_date=self.check_in_date,
                check_out_date=self.check_out_date,
                preferred_payment_timings=preferred_payment_timings,
                is_mobile=is_mobile,
            )

            # let's remove those hotels without rooms
            available_hotels = [h for h in available_hotels if "rooms" in h and h["rooms"]] if available_hotels else []

            # Keep track of already checked hotels that are not available
            already_checked_not_available_hotels_ids |= {h["property_id"] for h in hotels_to_check} - {
                h["property_id"] for h in available_hotels
            }

            if available_hotels and len(available_hotels) > 0:
                if city_name is None:
                    city_name = await self._extract_city_name_with_fallbacks(available_hotels, lat_long_dict)
                if company_rate is None and city_name is not None and hotel_policy is not None:
                    city_in_exception = list(
                        filter(
                            lambda exception: exception.city.lower() == (city_name.lower() if city_name else ""),
                            hotel_policy.exceptions or [],
                        )
                    )
                    if city_in_exception is not None and len(city_in_exception) > 0:
                        company_rate = city_in_exception[0].rate
                    else:
                        company_rate = hotel_policy.standard_rate

                if company_rate is not None:
                    for h in available_hotels:
                        within_policy = False
                        for r in h.get("rooms", []):
                            total_price_no_tax = r.get("price", {}).get("base")

                            price_per_night = total_price_no_tax / nights if total_price_no_tax is not None else None
                            is_in_policy = price_per_night <= company_rate if price_per_night is not None else False

                            r["within_policy"] = is_in_policy
                            r["price_per_night"] = price_per_night
                            policy_status = "Within" if is_in_policy else "Out of"
                            if city_name:
                                r["within_or_out_policy_reason"] = (
                                    f"{policy_status} company policy for {city_name.title()} at ${company_rate}"
                                )
                            else:
                                r["within_or_out_policy_reason"] = f"{policy_status} company policy at ${company_rate}"
                            if is_in_policy:
                                within_policy = True
                        h["has_within_policy_room"] = within_policy

                    # Sort hotels so those with at least one room within policy come first
                    available_hotels.sort(key=lambda h: not h.get("has_within_policy_room", False))

                for h in available_hotels:
                    room_prices = []
                    supported_cancellation_policies = set()
                    supported_payment_timings = set()
                    for r in h.get("rooms", []):
                        total_price_no_tax = r.get("price", {}).get("base")
                        policies = r.get("policies") or {}
                        cancellation_policy = policies.get("cancellation", {}).get("type", None)
                        if cancellation_policy:
                            supported_cancellation_policies.add(cancellation_policy)

                        payment_timings = policies.get("payment", {}).get("timings", [])
                        if payment_timings:
                            for payment_timing in payment_timings:
                                supported_payment_timings.add(payment_timing)

                        if total_price_no_tax is not None:
                            room_prices.append(total_price_no_tax / nights)

                    if supported_cancellation_policies:
                        h["supported_cancellation_policies"] = list(supported_cancellation_policies)

                    if supported_payment_timings:
                        h["supported_payment_timings"] = list(supported_payment_timings)

                    # Calculate and add price range for all rooms in this hotel
                    if room_prices:
                        h["room_price_range"] = {"min": min(room_prices), "max": max(room_prices)}
                    else:
                        h["room_price_range"] = None

            count_of_hotels = len(available_hotels)
            count_of_rooms = sum([len(h.get("rooms", [])) for h in available_hotels])
            if count_of_hotels == 0:
                self.websocket_send_message(
                    message={
                        "type": "hotels_skeleton_async",
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": HOTEL_SKELETON_MESSAGES["RADIUS_EXPAND"].format(
                            radius=current_radius / one_mile_in_meters
                        ),
                    },
                    schedule_send_message_fn=schedule_send_message_fn,
                )
            elif count_of_hotels > 1 and count_of_rooms > 0:
                self.websocket_send_message(
                    message={
                        "type": "hotels_skeleton_async",
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": HOTEL_SKELETON_MESSAGES["NARROWING_DOWN"].format(count=count_of_hotels),
                    },
                    schedule_send_message_fn=schedule_send_message_fn,
                )
            # For single hotel case (count_of_hotels == 1), we silently proceed without a message
            if count_of_rooms > 0:
                return available_hotels, current_radius, all_hotels
            logger.info(f"No room available, keep expanding by {radius_expanding_step}...", mask=hotel_log_mask)
            current_radius += radius_expanding_step

        return [], current_radius, all_hotels

    def websocket_send_message(
        self, message: dict[str, Any], schedule_send_message_fn: Callable[[dict[str, Any]], None] | None = None
    ):
        if schedule_send_message_fn:
            schedule_send_message_fn(message)

    async def select_hotel_rooms(
        self,
        rooms_options_data: str,
        max_rooms: int,
        travel_context: str,
        messages: list[str],
        preferred_payment_timings: list[HotelBookingPaymentTiming],
    ):
        rooms = await b.SelectHotelRooms(
            rooms_options_data=rooms_options_data,
            max_rooms=max_rooms,
            travel_context=travel_context,
            messages=messages,
            current_datetime=get_current_datetime_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            payment_timings=[p.value for p in preferred_payment_timings],
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        return rooms

    async def llm_find_matching_rooms(
        self,
        messages: list[str],
        travel_context_str: str,
        recommended_property_ids: list[int],
        hotel_detail_dicts: dict[int, Any],
        preferred_payment_timings: list[HotelBookingPaymentTiming],
    ) -> dict[str, Any]:
        ongoing_tasks = {}
        # send the rooms to the llm concurrently
        for property_id in recommended_property_ids:
            candidate_room_strs = []
            candidate_rooms = hotel_detail_dicts[property_id]["rooms"]
            if candidate_rooms:
                for room in candidate_rooms:
                    room_str = await self.format_room(room)
                    candidate_room_strs.append(room_str)

                ongoing_tasks[property_id] = asyncio.create_task(
                    self.select_hotel_rooms(
                        rooms_options_data="\n\n".join(candidate_room_strs),
                        max_rooms=max_llm_selected_rooms,
                        travel_context=travel_context_str,
                        messages=messages,
                        preferred_payment_timings=preferred_payment_timings,
                    )
                )

        recommended_rooms = {}
        # wait for all the tasks to complete
        for property_id, task in ongoing_tasks.items():
            recommended_rooms[property_id] = await task

        # now we need to match the rooms to the original rooms
        result = {}
        for property_id, rooms in recommended_rooms.items():
            if property_id not in hotel_detail_dicts:
                logger.warning(f"Property ID {property_id} not found in hotel details")
                continue

            incoming_rooms = hotel_detail_dicts[property_id]["rooms"]
            matched_rooms = []
            for room in rooms:
                room_id = room.room_id
                matched_room = next(
                    (incoming_room for incoming_room in incoming_rooms if incoming_room["room_id"] == room_id), None
                )
                if matched_room:
                    copy_of_matched_room = deepcopy(matched_room)
                    copy_of_matched_room["ranking_reason"] = room.reason
                    copy_of_matched_room["rank"] = room.rank
                    matched_rooms.append(copy_of_matched_room)

            # Sort rooms by rank and trim to max_llm_selected_rooms
            matched_rooms.sort(key=lambda x: x["rank"])
            result[property_id] = matched_rooms[:max_llm_selected_rooms]

        return result

    async def _execute_hotel_search(
        self,
        params: dict[str, Any],
        schedule_send_message_fn: Callable[[dict[str, Any]], None],
        search_type: HotelSearchType,
        hotel_select_result: Optional[HotelSelectResult] = None,
    ):
        """Shared logic for both regular and equivalent hotel searches."""
        response = None

        hotel_policy = None
        company_admin = (
            await UserDB.from_organization_id_and_role(self.user.organization_id, UserRole.company_admin)
            if self.user.organization_id
            else None
        )
        policy_user_id = company_admin.id if company_admin else self.user.id

        company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)
        if (
            company_policy is not None
            and company_policy.parsed_travel_policy is not None
            and company_policy.parsed_travel_policy.get("hotel_policy") is not None
        ):
            parsed_hotel_policy: dict[str, Any] | None = company_policy.parsed_travel_policy.get("hotel_policy")
            assert parsed_hotel_policy is not None
            hotel_policy = HotelPolicy(**parsed_hotel_policy)

        json_dict = params

        # Will need these later for availability and pricing
        self.check_in_date = json_dict["check_in_date"]
        self.check_out_date = json_dict["check_out_date"]

        location_latitude_longitude_str = json_dict.get("location_latitude_longitude", None)
        input_hotel_search_radius = json_dict.get("hotel_search_radius") or None

        lat_long_dict = self.resolve_lat_long(location_latitude_longitude_str)
        if lat_long_dict == {}:
            raise ValueError(f"Couldn't resolve latitude/longitude coordinates: {location_latitude_longitude_str}.")

        # Search and availability check
        preferred_payment_timings = [
            timing
            for payment_timing in (json_dict.get("preferred_payment_timings") or [])
            if (timing := HotelBookingPaymentTiming.from_baml_payment_timing(payment_timing)) is not None
        ]
        hotels, hotel_search_radius, candidates = await self.get_available_hotels_and_price(
            json_dict,
            lat_long_dict,
            start_radius=input_hotel_search_radius,
            hotel_policy=hotel_policy,
            preferred_payment_timings=preferred_payment_timings,
            schedule_send_message_fn=schedule_send_message_fn,
        )
        count_of_hotels = len(hotels)
        count_of_rooms = sum([len(h.get("rooms", [])) for h in hotels])
        if count_of_hotels > 1 and count_of_rooms > 0:
            # Different message text based on search type
            message_suffix = (
                "." if search_type == HotelSearchType.EQUIVALENT else " to the ones that best match your preferences."
            )
            schedule_send_message_fn(
                {
                    "type": "hotels_skeleton_async",
                    "text": HOTEL_SKELETON_MESSAGES["NARROWING_DOWN"].format(count=count_of_hotels) + message_suffix,
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )

        # THE HOTELS SHOULD BE SORTED BY DISTANCE TO THE TARGET LOCATION
        hotels = hotels[:number_of_hotels_considered_by_the_model]

        if len(hotels) == 0:
            agent_response = "Sorry, I couldn't find any available hotels for your selected dates. Please try adjusting your dates to see more options."
            if len(candidates) > 0:
                # Different travel context handling based on search type
                travel_context = str(json_dict.get("travel_context") or "")
                t = Timings("BAML: NoHotelAvailableMessage")
                agent_response = await b.NoHotelAvailableMessage(
                    candidate_hotel_names=[hotel["property_name"] for hotel in candidates],
                    search_radius_miles=f"{hotel_search_radius / one_mile_in_meters:.2f}",
                    travel_context=travel_context,
                    self_intro=settings.OTTO_SELF_INTRO,
                    convo_style=settings.OTTO_CONVO_STYLE,
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("yellow")
                logger.log_baml()

            found_nothing_message = AIMessage(content=agent_response)
            resp = HotelSearchOptionsResponse(
                presentation_message=agent_response,
                hotel_options=[],
                error_response="",
            )
            found_nothing_message.additional_kwargs = {
                "function_call": {
                    "arguments": resp.model_dump_json(),
                    "name": "HotelSearchOptionsResponse",
                },
                "agent_classification": AgentTypes.HOTELS,
            }
            found_nothing_message.additional_kwargs["raw_tool_output"] = self.filter_raw_tool_output(candidates)
            return found_nothing_message

        # Raw hotels data for debugging purposes
        full_hotels_list_str = self.filter_raw_tool_output(hotels)

        # PRUNE DATA AND KEEP WHAT IS ONLY ABSOLUTELY NECCESARY FOR THE MODEL TO CHOOSE THE 4 BEST
        # Save off the rooms data and reinject it after the model chooses
        # the top hotels
        saved_data = {}
        for hotel in hotels:
            property_id = hotel["property_id"]
            saved_data[property_id] = deepcopy(hotel)
            del hotel["rooms"]
            del hotel["photo_main_url"]
            del hotel["dropoff_url"]

        logger.info(f"Sending {len(hotels)} hotels to the model for analysis and selection.", mask=hotel_log_mask)

        # Cut down on the payload size by creating a string csv
        # representation
        hotels_str = self.text_from_dict(hotels)
        if hotels_str is None:
            raise ValueError("Failed to process available hotels for the model when converting JSON to CSV")
        user_preferences = await get_user_preferences(self.user.id)
        # Different BAML calls based on search type
        if search_type == HotelSearchType.EQUIVALENT:
            t = Timings("BAML: ConverseEquivalentHotelSearch")
            assert hotel_select_result is not None, "hotel_select_result is required for equivalent hotel search"
            response = await b.ConverseEquivalentHotelSearch(
                hotel_options_data=hotels_str,
                previous_hotel_select_result=hotel_select_result,
                preferences=user_preferences,
                current_datetime=get_current_datetime_string(self.timezone),
                messages=params.get("messages") or [],
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
        else:
            t = Timings("BAML: ConverseHotelSearch")
            response = await b.ConverseHotelSearch(
                hotel_options_data=hotels_str,
                travel_context=str(json_dict.get("travel_context") or ""),
                preferences=user_preferences,
                current_datetime=get_current_datetime_string(self.timezone),
                messages=params.get("messages") or [],
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
        t.print_timing("yellow")
        logger.log_baml()

        new_message = AIMessage(content="")

        # Rejoin the rooms data into the hotels chosen by the model
        hotel_search_options_schema = response.model_dump()
        hotel_options = hotel_search_options_schema.get("hotel_options", [])
        hotel_options = [o for o in hotel_options if o["property_id"] in saved_data]

        # Dedupe hotels by property_id
        deduped_hotel_ids = set()
        deduped_hotels = []
        for hotel in hotel_options:
            hotel_id = hotel["property_id"]
            if hotel_id not in deduped_hotel_ids:
                deduped_hotel_ids.add(hotel_id)
                deduped_hotels.append(hotel)

        hotel_options = deduped_hotels
        if len(hotel_options) == 0:
            new_message.content = response.presentation_message
            resp = HotelSearchOptionsResponse(
                presentation_message=response.presentation_message,
                hotel_options=[],
                error_response="",
            )
            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": resp.model_dump_json(),
                    "name": "HotelSearchOptionsResponse",
                },
                "agent_classification": AgentTypes.HOTELS,
            }
            return new_message

        hotel_property_ids = [o["property_id"] for o in hotel_options]

        # llm to select the best rooms
        matched_rooms = await self.llm_find_matching_rooms(
            params.get("messages") or [],
            str(json_dict.get("travel_context") or ""),
            hotel_property_ids,
            saved_data,
            preferred_payment_timings,
        )

        for hotel_option in hotel_options:
            property_id = hotel_option["property_id"]
            # Reinject saved metadata
            hotel_option["rooms"] = matched_rooms[property_id]
            hotel_option["dropoff_url"] = saved_data[property_id]["dropoff_url"]
            hotel_option["photos"] = saved_data[property_id]["photos"]
            hotel_option["image_url"] = saved_data[property_id]["photo_main_url"]
            hotel_option["property_name"] = saved_data[property_id]["property_name"]
            hotel_option["gps_coordinates"] = (
                f"{saved_data[property_id]['location']['coordinates']['latitude']}, {saved_data[property_id]['location']['coordinates']['longitude']}"
            )
            hotel_option["description"] = saved_data[property_id]["description_text"]
            hotel_option["amenities"] = saved_data[property_id]["facilities"]
            hotel_option["check_in_time"] = saved_data[property_id]["checkin_checkout_times"]["checkin_from"]
            hotel_option["check_out_time"] = saved_data[property_id]["checkin_checkout_times"]["checkout_to"]
            # probably haves
            hotel_option["hotel_class"] = str(saved_data[property_id]["rating"].get("stars", "Not available"))
            hotel_option["overall_rating"] = str(saved_data[property_id]["rating"].get("review_score", "Not available"))
            # nearby_places string @description(#"The full list of names of nearby places."#)

        # OK, now go and get the latest availability and prices and inject the extra info into the property dicts
        # JTB:  2024-11-08 note that we might want to decouble this call in the future in order to let the user pick back
        # up after abandoning a thread due to state availability results
        hotel_search_options_schema["hotel_options"] = await self.booking_dot_com_tools.get_availability_and_price(
            hotel_options,
            check_in_date=self.check_in_date,
            check_out_date=self.check_out_date,
            preferred_payment_timings=preferred_payment_timings,
        )
        if len(hotel_search_options_schema["hotel_options"]) == 0:
            new_message.content = "No availability found for selected hotels, please try other dates."
            resp = HotelSearchOptionsResponse(
                presentation_message=new_message.content,
                hotel_options=[],
                error_response="",
            )
            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": resp.model_dump_json(),
                    "name": "HotelSearchOptionsResponse",
                },
                "agent_classification": AgentTypes.HOTELS,
            }
            return new_message

        # We need this info to display the location target radius
        hotel_search_options_schema["location_lat_long"] = {
            **lat_long_dict,
            "radius": hotel_search_radius,
        }

        for hotel_option in hotel_search_options_schema["hotel_options"]:
            hotel_option["check_in_date"] = json_dict["check_in_date"]
            hotel_option["check_out_date"] = json_dict["check_out_date"]

        results_str = json.dumps(hotel_search_options_schema)

        new_message.additional_kwargs = {
            "expire_timestamp": (datetime.now(timezone.utc) + settings.CARD_EXPIRE_DELTA).isoformat(),
            "function_call": {
                "arguments": results_str,
                "name": "HotelSearchOptionsResponse",
            },
            "agent_classification": AgentTypes.HOTELS,
        }
        new_message.additional_kwargs["raw_tool_output"] = full_hotels_list_str
        return new_message

    async def equivalent_hotels_search(
        self,
        params: dict[str, Any],
        hotel_select_result: HotelSelectResult,
        schedule_send_message_fn: Callable[[dict[str, Any]], None],
    ):
        return await self._execute_hotel_search(
            params=params,
            schedule_send_message_fn=schedule_send_message_fn,
            search_type=HotelSearchType.EQUIVALENT,
            hotel_select_result=hotel_select_result,
        )

    async def hotels_search(self, params: dict[str, Any], schedule_send_message_fn: Callable[[dict[str, Any]], None]):
        return await self._execute_hotel_search(
            params=params,
            schedule_send_message_fn=schedule_send_message_fn,
            search_type=HotelSearchType.REGULAR,
        )

    async def hotel_booking(self, params: HotelBookingParams) -> dict | str:
        try:
            # Set this to True to enable fake booking.
            # By default only enabled in the dev environment.
            order_token = params["order_token"]
            assert order_token is not None, "Missing order token, cannot book"
            payment_timing = HotelBookingPaymentTiming(
                params["payment_timing"] or HotelBookingPaymentTiming.PAY_AT_THE_PROPERTY
            )
            return await self.booking_dot_com_tools.do_order_create(
                params["room_product_id"],
                user=self.user,
                order_token=order_token,
                payment_timing=payment_timing,
            )

        except Exception as e:
            logger.error(e, mask=hotel_log_mask)
            return str(e)

    async def hotels_booking(self, params: dict[str, Any]):
        response = None
        try:
            order_token = params.get("order_token")
            assert order_token is not None, "Missing order token, cannot book"
            room_product_id = params.get("room_product_id")
            assert room_product_id is not None, "Missing room_product_id, cannot book"

            payment_timing = HotelBookingPaymentTiming(
                params.get("payment_timing", HotelBookingPaymentTiming.PAY_AT_THE_PROPERTY)
            )

            booking_params: HotelBookingParams = {
                "order_token": order_token,
                "room_product_id": room_product_id,
                "payment_timing": payment_timing,
            }

            create_payload = await self.hotel_booking(booking_params)

            if isinstance(create_payload, str):
                raise ValueError(create_payload)

            if create_payload is None:
                raise ValueError("Create booking failed.")

            if create_payload.get("status") == booking_failed_status:
                t = Timings("BAML: GenerateErrorMessage")
                error_message = await b.GenerateErrorMessage(
                    travel_context="",
                    messages=[],
                    error_message=create_payload.get("error_response", ""),
                    current_date=get_current_date_string(self.timezone),
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("yellow")
                logger.log_baml()

                new_message = AIMessage(content=error_message.agent_response)
                new_message.additional_kwargs = {
                    "function_call": {
                        "arguments": error_message.model_dump_json(),
                        "name": "HotelBookingResponse",
                        "status": booking_failed_status,
                    },
                    "agent_classification": AgentTypes.HOTELS,
                }

                return new_message

            data = create_payload["data"]
            new_message = AIMessage(content="")
            response = HotelBookingResponse.model_construct(values={})
            if data:  # actual payload from booking.com
                create_payload_str = json.dumps(data)

                order_preview = HotelOrderPreview(
                    property_id=params.get("property_id", None),  # type: ignore
                    room_product_id=params.get("room_product_id", None),  # type: ignore
                    hotel_name=params.get("hotel_name", None),  # type: ignore
                    room_title=params.get("room_title", None),  # type: ignore
                    validated_price=params.get("validated_price", None),  # type: ignore
                    # Default to USD for now
                    currency=params.get("currency", "USD"),  # type: ignore
                )
                t = Timings("BAML: ConverseHotelBooking")
                response = await b.ConverseHotelBooking(
                    order_preview=order_preview,
                    booking_order_data=create_payload_str,
                    travel_context="",
                    self_intro=settings.OTTO_SELF_INTRO,
                    convo_style=settings.OTTO_CONVO_STYLE,
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("yellow")
                logger.log_baml()

                json_representation = response.model_dump_json()
                json_obj = json.loads(json_representation)
                # Tak on some extra info for a full intinerary
                json_obj["validated_price"] = params.get("validated_price", None)
                json_obj["currency"] = params.get("currency", "USD")
                json_obj["check_in_date"] = params.get("check_in_date", None)
                json_obj["check_out_date"] = params.get("check_out_date", None)
                json_obj["property_id"] = params.get("property_id", None)
                json_obj["room_product_id"] = params.get("room_product_id", None)
                json_obj["room_title"] = params.get("room_title", None)
                json_obj["payment_timing"] = payment_timing.value

            else:  # error payload
                json_obj = {}
                json_obj["status"] = create_payload.get("status")
                json_obj["error_response"] = create_payload.get("error_response")

            json_representation = json.dumps(json_obj)
            logger.info(json_representation, mask=hotel_log_mask)

            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": json_representation,
                    "name": "HotelBookingResponse",
                },
                "agent_classification": AgentTypes.HOTELS,
            }

            return new_message

        except Exception as e:
            logger.error(e.args[0], mask=hotel_log_mask)
            error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_booking")
            error_msg.additional_kwargs = {
                "function_call": {
                    "arguments": json.dumps({"error_response": e.args[0]}),
                    "name": "Error Message",
                }
            }
            return error_msg

    def resolve_lat_long(self, location_latitude_longitude_str):
        try:
            lat_long_dict = {}
            if location_latitude_longitude_str is not None and location_latitude_longitude_str != "":
                if type(location_latitude_longitude_str) is str:
                    logger.info(f"location_latitude_longitude: {location_latitude_longitude_str}", mask=hotel_log_mask)
                    if "," in location_latitude_longitude_str:
                        lat_long_list = location_latitude_longitude_str.split(",", 1)
                        latitude = float(lat_long_list[0])
                        longitude = float(lat_long_list[1])
                        lat_long_dict = {"latitude": latitude, "longitude": longitude}

                        return lat_long_dict
                else:
                    raise ValueError(
                        f"location_latitude_longitude is not in str format! {location_latitude_longitude_str}"
                    )
            else:
                raise ValueError("No lat/long provided by the model.")
        except Exception as e:
            logger.error(f"Failed to resolve lat long based on model output, error: {e}", mask=hotel_log_mask)

        return {}

    async def _extract_city_name_with_fallbacks(self, available_hotels: list, lat_long_dict: dict) -> str:
        """
        Extract city name using multiple fallback strategies as suggested by user.

        Args:
            available_hotels: List of available hotels with location data
            lat_long_dict: Dictionary containing latitude and longitude coordinates

        Returns:
            City name string or empty string if no city can be determined
        """
        for hotel in available_hotels:
            city = hotel.get("location", {}).get("city", "")
            if city and city.strip():
                logger.debug(f"Found city name from hotel data: {city}", mask=hotel_log_mask)
                return city.strip()

        if lat_long_dict and lat_long_dict.get("latitude") and lat_long_dict.get("longitude"):
            try:
                from server.services.google_maps_api.get_address import get_address_from_coordinates

                full_address = await get_address_from_coordinates(lat_long_dict["latitude"], lat_long_dict["longitude"])

                if full_address:
                    address_parts = [part.strip() for part in full_address.split(",")]
                    if len(address_parts) >= 2:
                        potential_city = address_parts[-3] if len(address_parts) >= 3 else address_parts[-2]
                        if potential_city and not potential_city.isdigit():
                            logger.debug(f"Found city name from Google Maps API: {potential_city}", mask=hotel_log_mask)
                            return potential_city.strip()

            except Exception as e:
                logger.error(f"Failed to get city from Google Maps API: {e}", mask=hotel_log_mask)

        logger.warning("Could not determine city name using any fallback strategy", mask=hotel_log_mask)
        return ""

    # This data is sent to the front end for debugging purposes
    # JTB:  2024-20-11 - Note that we don't know the prices for this larger
    # list of hotels because we haven't checked their availability
    def filter_raw_tool_output(self, hotels: List[dict]) -> str:
        try:
            hotel_list = []
            for hotel in hotels:
                name = hotel["property_name"]
                gps = hotel["location"]["coordinates"]
                lat = gps["latitude"]
                long = gps["longitude"]
                hotel_dict = {
                    "name": name,
                    "price": "No price available",
                    "lat": lat,
                    "long": long,
                }
                hotel_list.append(hotel_dict)
            return_dict = {"hotel_choices": hotel_list}
            json_str = json.dumps(return_dict)
            return json_str
        except Exception as e:
            logger.error(f"Failed to generate filtered raw tool output, error: {e}", mask=hotel_log_mask)
            pass

        return ""

    def text_from_dict(self, hotels: List[dict]) -> str:
        # Write out the dictionary to a string, one field at a time

        try:
            output = StringIO()

            for hotel in hotels:
                output.write(f"{hotel['property_name']} \n")
                output.write(f"hotel ID: {hotel['property_id']} \n")
                description = hotel["description_text"].replace(
                    "\n", " "
                )  # I noticed a bunch of \n characters embedded in this string
                output.write(f"{description} \n")
                # Write first available address if it exists
                address = hotel["location"]["address"]
                if address:
                    first_address = next(iter(address.values()), None)
                    if first_address:
                        output.write(f"{first_address} \n")

                if hotel.get("has_within_policy_room", False):
                    output.write("Within company policy: True \n")
                else:
                    output.write("Within company policy: False \n")

                output.write(f"{hotel['location']['city']} \n")
                output.write(f"{hotel['location']['postal_code']} \n")
                output.write(
                    f"Lat/Long: {hotel['location']['coordinates']['latitude']}, {hotel['location']['coordinates']['longitude']} \n"
                )
                districts = hotel.get("districts", None)
                if districts is not None:
                    districts_str = ", ".join(districts)
                    output.write(f"Districts: {districts_str} \n")

                price_range = hotel.get("room_price_range") or None
                if price_range:
                    output.write(f"Price range: ${price_range['min']:.2f} - ${price_range['max']:.2f} \n")

                output.write(f"Review score: {hotel['rating']['review_score']} \n")
                output.write(f"Stars: {hotel['rating']['stars']} \n")
                output.write(f"Check in time: {hotel['checkin_checkout_times']['checkin_from']} \n")
                output.write(f"Check out time: {hotel['checkin_checkout_times']['checkout_to']} \n")
                facilities_str = ", ".join(hotel.get("facilities", "None"))
                output.write(f"Amenities: {facilities_str} \n")

                output.write(f"Distance: {hotel['distance_to_target_in_miles']:.2f} miles \n")

                if hotel.get("supported_cancellation_policies"):
                    output.write(
                        f"Supported cancellation policies: {', '.join(hotel['supported_cancellation_policies'])} \n"
                    )

                if hotel.get("supported_payment_timings"):
                    output.write(f"Supported payment timings: {', '.join(hotel['supported_payment_timings'])} \n")

                output.write("\n")

            # Write the CSV content to a file
            with open("hotel_csvdata.txt", "w", newline="") as file:
                file.write(output.getvalue())

            return output.getvalue()

        except Exception as e:
            logger.error(f"Failed to generate text from dict, Error: {e}", mask=hotel_log_mask)

        return ""

    async def format_room(self, room: dict) -> str:
        """Format a room object into a string for LLM consumption."""
        lines = []

        # Basic room info
        lines.append(f"Room: {room['title']}")
        lines.append(f"Room ID: {room['room_id']}")
        lines.append(f"Description: {room['description']}")

        # Room Amenities
        lines.append(f"Amenities: {', '.join(room['amenities'])}")

        # Price information
        price = room["price"]
        if price.get("base") is not None:
            lines.append(f"Base Price: ${price['base']:.2f}")
        if price.get("book") is not None:
            lines.append(f"Book Price: ${price['book']:.2f}")
        if price.get("total") is not None:
            lines.append(f"Total Price: ${price['total']:.2f}")

        if room.get("price_per_night") is not None:
            lines.append(f"Price per night: ${room['price_per_night']:.2f}")

        # Helper function to format charges
        async def format_charges(charges, header):
            if charges:
                result = [header]
                for charge in charges:
                    charge_type = await BookingTools.get_charge_type_by_id(charge["charge"])
                    if charge["mode"] == "percentage":
                        if charge.get("total_amount") is not None:
                            result.append(f"- {charge_type}: {charge['percentage']}% (${charge['total_amount']:.2f})")
                    elif charge["mode"] == "incalculable":
                        result.append(f"- {charge_type}: Depends on consumption during stay")
                    else:
                        if charge.get("total_amount") is not None:
                            result.append(f"- {charge_type}: ${charge['total_amount']:.2f}")
                return result
            return []

        # Included extra charges
        lines.extend(
            await format_charges((price.get("extra_charges") or {}).get("included"), "Charges Included in Book Price:")
        )

        # Excluded extra charges
        lines.extend(
            await format_charges(
                (price.get("extra_charges") or {}).get("excluded"), "Additional Charges Not in Book Price:"
            )
        )

        # Cancellation policy
        cancellation = room["policies"]["cancellation"]
        if cancellation["free_cancellation_until"]:
            lines.append(f"Free Cancellation Until: {cancellation['free_cancellation_until']}")
        else:
            lines.append("Non-refundable")

        # Payment timing
        payment_timings = room["policies"]["payment"]["timings"]
        lines.append(f"Payment Timings: {', '.join(payment_timings)}")

        return "\n".join(lines)
